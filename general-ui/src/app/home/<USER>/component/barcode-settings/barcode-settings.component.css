.barcode-settings-container {
  padding: 20px;
}

.settings-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.section-title i {
  margin-right: 8px;
  color: #007bff;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 5px;
}

.form-control {
  border-radius: 4px;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.custom-control-label {
  font-weight: 500;
  color: #495057;
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::before {
  background-color: #007bff;
  border-color: #007bff;
}

.input-group-text {
  background-color: #e9ecef;
  border-color: #ced4da;
  color: #495057;
  font-weight: 500;
}

.preview-section {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
  position: sticky;
  top: 20px;
}

.preview-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.preview-item {
  margin-bottom: 15px;
}

.preview-item label {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  margin-bottom: 5px;
  display: block;
}

.preview-text {
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  min-height: 35px;
  display: flex;
  align-items: center;
}

.btn {
  border-radius: 4px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #545b62;
  border-color: #545b62;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
  color: #212529;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.alert {
  border-radius: 4px;
  font-size: 14px;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.form-text {
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .barcode-settings-container {
    padding: 10px;
  }
  
  .settings-section {
    padding: 15px;
  }
  
  .preview-section {
    position: static;
    margin-top: 20px;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 10px;
  }
  
  .d-flex.justify-content-between > div {
    display: flex;
    gap: 10px;
  }
}

/* Loading state */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Form validation styles */
.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control.is-valid {
  border-color: #28a745;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #dc3545;
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #28a745;
}
