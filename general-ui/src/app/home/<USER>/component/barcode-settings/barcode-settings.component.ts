import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { BarcodeSettingsService } from '../../../core/service/barcode-settings.service';
import { BarcodeSettings, BarcodeSettingsModel } from '../../../core/model/barcode-settings';
import { CostCodeSetupComponent } from '../cost-code-setup/cost-code-setup.component';

@Component({
  selector: 'app-barcode-settings',
  templateUrl: './barcode-settings.component.html',
  styleUrls: ['./barcode-settings.component.css']
})
export class BarcodeSettingsComponent implements OnInit {
  
  // Settings data
  barcodeSettings: BarcodeSettings;
  isLoading = true;
  isSaving = false;

  // Options for dropdowns
  paperSizeOptions = BarcodeSettingsModel.getPaperSizeOptions();
  fontFamilyOptions = BarcodeSettingsModel.getFontFamilyOptions();
  columnOptions = BarcodeSettingsModel.getColumnOptions();

  constructor(
    private barcodeSettingsService: BarcodeSettingsService,
    private toastr: ToastrService,
    private modalService: BsModalService
  ) {
    this.barcodeSettings = BarcodeSettingsModel.getDefaultSettings();
  }

  ngOnInit(): void {
    this.loadSettings();
  }

  /**
   * Load barcode settings
   */
  loadSettings(): void {
    this.isLoading = true;

    // First load from localStorage for immediate display
    this.loadFromLocalStorage();

    // Then load from database to get the latest values
    this.barcodeSettingsService.loadBarcodeSettingsFromDatabase().subscribe(
      (settings) => {
        // Merge database settings with localStorage settings
        // Keep localStorage values as user preferences, but update with database values if localStorage doesn't have them
        if (!localStorage.getItem('app_settings')) {
          this.barcodeSettings = settings;
        }
        this.isLoading = false;
        console.log('Barcode settings loaded and merged:', this.barcodeSettings);
      },
      (error) => {
        console.error('Error loading barcode settings from database:', error);
        // Keep localStorage settings if database fails
        this.isLoading = false;
        this.toastr.warning('Using cached settings. Database connection failed.', 'Warning');
      }
    );
  }

  /**
   * Save barcode settings
   */
  saveSettings(): void {
    this.isSaving = true;

    // Save to database (this will also save to localStorage automatically)
    this.barcodeSettingsService.saveBarcodeSettingsToDatabase(this.barcodeSettings).subscribe(
      (success) => {
        this.isSaving = false;
        if (success) {
          this.toastr.success('Barcode settings saved successfully', 'Success');
        } else {
          this.toastr.warning('Settings saved locally but some database operations failed', 'Warning');
        }
        console.log('Barcode settings saved to both localStorage and database');
      },
      (error) => {
        this.isSaving = false;
        console.error('Error saving barcode settings to database:', error);
        // Fallback: save to localStorage only if database fails
        this.barcodeSettingsService.saveBarcodeSettings(this.barcodeSettings);
        this.toastr.warning('Settings saved locally but database save failed', 'Warning');
      }
    );
  }



  /**
   * Reset to default settings
   */
  resetToDefaults(): void {
    if (confirm('Are you sure you want to reset all barcode settings to defaults? This action cannot be undone.')) {
      this.barcodeSettings = BarcodeSettingsModel.getDefaultSettings();
      this.saveSettings();
      this.toastr.info('Barcode settings reset to defaults', 'Reset');
    }
  }

  /**
   * Load settings from localStorage for immediate display
   */
  loadFromLocalStorage(): void {
    this.barcodeSettings = this.barcodeSettingsService.getBarcodeSettingsFromGeneralSettings();
    console.log('Barcode settings loaded from localStorage:', this.barcodeSettings);
  }

  /**
   * Clear settings from localStorage
   */
  clearLocalStorage(): void {
    if (confirm('Are you sure you want to clear barcode settings from browser memory? This will reset to default settings.')) {
      this.barcodeSettingsService.clearBarcodeSettingsFromLocalStorage();
      this.loadSettings();
      this.toastr.info('Browser memory cleared. Settings reset to defaults.', 'Cleared');
    }
  }

  /**
   * Preview the current font settings
   */
  getPreviewStyle(element: string): any {
    const baseStyle = {
      fontFamily: this.barcodeSettings.fontFamily,
      fontWeight: 'bold',
      color: '#333'
    };

    switch (element) {
      case 'itemName':
        return { ...baseStyle, fontSize: this.barcodeSettings.itemNameFontSize + 'px' };
      case 'price':
        return { ...baseStyle, fontSize: this.barcodeSettings.priceFontSize + 'px' };
      case 'costCode':
        return { ...baseStyle, fontSize: this.barcodeSettings.costCodeFontSize + 'px' };
      case 'barcodeText':
        return { ...baseStyle, fontSize: this.barcodeSettings.barcodeTextFontSize + 'px' };
      default:
        return baseStyle;
    }
  }

  /**
   * Get font size range validation
   */
  getFontSizeRange(): { min: number, max: number } {
    return { min: 6, max: 24 };
  }

  /**
   * Validate font size
   */
  validateFontSize(value: number, element: string): void {
    const range = this.getFontSizeRange();
    if (value < range.min || value > range.max) {
      this.toastr.warning(`Font size for ${element} should be between ${range.min} and ${range.max} pixels`, 'Validation');
    }
  }

  /**
   * Update font size with validation
   */
  updateFontSize(element: string, value: number): void {
    const range = this.getFontSizeRange();
    const validValue = Math.max(range.min, Math.min(range.max, value));

    switch (element) {
      case 'itemName':
        this.barcodeSettings.itemNameFontSize = validValue;
        break;
      case 'price':
        this.barcodeSettings.priceFontSize = validValue;
        break;
      case 'costCode':
        this.barcodeSettings.costCodeFontSize = validValue;
        break;
      case 'barcodeText':
        this.barcodeSettings.barcodeTextFontSize = validValue;
        break;
    }
  }

  /**
   * Open cost code settings modal
   */
  openCostCodeSettings(): void {
    const modalRef: BsModalRef = this.modalService.show(CostCodeSetupComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: {
        isModal: true
      }
    });

    // Subscribe to modal hidden event to reload settings if needed
    modalRef.onHidden.subscribe(() => {
      // Reload settings to get any updated cost code settings
      this.loadSettings();
    });
  }
}
