<div class="container-fluid px-0">
  <h2 class="component-title">Settings</h2>
  <div class="alert alert-info">
    <i class="fas fa-info-circle"></i> {{ 'SETTINGS.OVERRIDE_INFO' | translate }}
  </div>

  <!-- Navigation Tabs -->
  <ul class="nav nav-tabs mb-4" role="tablist">
    <li class="nav-item" role="presentation">
      <a class="nav-link" [class.active]="isTabActive('general')" (click)="switchTab('general')" role="tab"
         style="cursor: pointer;">
        <i class="fas fa-cog"></i> General Settings
      </a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" [class.active]="isTabActive('print')" (click)="switchTab('print')" role="tab"
         style="cursor: pointer;">
        <i class="fas fa-print"></i> Print Settings
      </a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" [class.active]="isTabActive('currency')" (click)="switchTab('currency')" role="tab"
         style="cursor: pointer;">
        <i class="fas fa-dollar-sign"></i> Currency Settings
      </a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" [class.active]="isTabActive('barcode')" (click)="switchTab('barcode')" role="tab"
         style="cursor: pointer;">
        <i class="fas fa-barcode"></i> Barcode Settings
      </a>
    </li>
  </ul>

  <!-- Tab Content -->
  <div class="tab-content">
    <!-- General Settings Tab -->
    <div class="tab-pane" [class.active]="isTabActive('general')" *ngIf="isTabActive('general')">
      <form #generalSettingsForm="ngForm">
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="settings-section">
              <!-- Default Discount Mode -->
              <div class="form-group mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label for="discountMode">{{ 'SETTINGS.DEFAULT_DISCOUNT_MODE' | translate }}</label>
                  <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="discountModeSwitch"
                           [checked]="isSettingEnabled('defaultDiscountMode')"
                           (change)="toggleSetting('defaultDiscountMode')">
                    <label class="custom-control-label" for="discountModeSwitch">
                      {{ isSettingEnabled('defaultDiscountMode') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                    </label>
                  </div>
                </div>
                <select
                  id="discountMode"
                  name="discountMode"
                  class="form-control"
                  [(ngModel)]="settingsMap['defaultDiscountMode']"
                  [disabled]="!isSettingEnabled('defaultDiscountMode')">
                  <option *ngFor="let option of discountModeOptions" [value]="option.value">
                    {{ option.label }}
                  </option>
                </select>
                <small class="form-text text-muted">
                  {{ 'SETTINGS.DISCOUNT_MODE_HELP' | translate }}
                </small>
              </div>


              <!-- Invoice Creation Mode has been moved to User Settings -->
              <!-- This setting is now user-specific and can be found in the User Settings page -->

              <!-- Minimum Markup Percentage for CASHIER role -->
              <div class="form-group mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label for="minimumMarkupPercentage">Minimum Markup Percentage</label>
                  <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="minimumMarkupPercentageSwitch"
                           [checked]="isSettingEnabled('minimumMarkupPercentage')"
                           (change)="toggleSetting('minimumMarkupPercentage')">
                    <label class="custom-control-label" for="minimumMarkupPercentageSwitch">
                      {{ isSettingEnabled('minimumMarkupPercentage') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                    </label>
                  </div>
                </div>
                <div class="input-group">
                  <input
                    type="number"
                    id="minimumMarkupPercentage"
                    name="minimumMarkupPercentage"
                    class="form-control"
                    [(ngModel)]="settingsMap['minimumMarkupPercentage']"
                    [disabled]="!isSettingEnabled('minimumMarkupPercentage')"
                    min="0"
                    max="100"
                    step="0.1">
                  <div class="input-group-append">
                    <span class="input-group-text">%</span>
                  </div>
                </div>
                <small class="form-text text-muted">
                  Minimum percentage markup above cost for users with CASHIER role. Default is 4%.
                  <br>
                  <strong>Note:</strong> This is now a user-specific setting. This value will be used as a default for
                  users who don't have their own setting.
                </small>
              </div>

              <!-- Allow Selling Under Cost -->
              <div class="form-group mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label for="allowSellingUnderCost">Allow Selling Under Cost</label>
                </div>
                <div class="form-check mb-2">
                  <input class="form-check-input" type="radio" name="allowSellingUnderCost"
                         id="allowSellingUnderCostYes"
                         [value]="'true'" [(ngModel)]="settingsMap['allowSellingUnderCost']">
                  <label class="form-check-label" for="allowSellingUnderCostYes">Yes, allow selling below cost</label>
                </div>
                <div class="form-check mb-2">
                  <input class="form-check-input" type="radio" name="allowSellingUnderCost" id="allowSellingUnderCostNo"
                         [value]="'false'" [(ngModel)]="settingsMap['allowSellingUnderCost']">
                  <label class="form-check-label" for="allowSellingUnderCostNo">No, prevent selling below cost</label>
                </div>
                <small class="form-text text-muted mt-2">
                  When set to "No", the system prevents adding items to invoices with selling prices below cost.
                  <br>
                  <strong>Note:</strong> This is a global setting that affects all users.
                </small>
              </div>

            </div>
          </div>

          <div class="col-md-6">
            <div class="settings-section">
              <!-- Additional general settings can be added here -->
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between">
          <button type="button" class="btn btn-secondary" (click)="resetToDefaults()">
            <i class="fas fa-undo"></i> {{ 'SETTINGS.RESET_TO_DEFAULTS' | translate }}
          </button>
          <button type="button" class="btn btn-primary" (click)="saveSettings()">
            <i class="fas fa-save"></i> {{ 'SETTINGS.SAVE_SETTINGS' | translate }}
          </button>
        </div>
      </form>
    </div>

    <!-- Print Settings Tab -->
    <div class="tab-pane" [class.active]="isTabActive('print')" *ngIf="isTabActive('print')">
      <form #printSettingsForm="ngForm">
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="settings-section">
              <!-- Silent Printing -->
              <div class="form-group mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label for="useSilentPrint">{{ 'SETTINGS.SILENT_PRINTING' | translate }}</label>
                  <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="silentPrintSwitch"
                           [checked]="isSettingEnabled('useSilentPrint')"
                           (change)="toggleSetting('useSilentPrint')">
                    <label class="custom-control-label" for="silentPrintSwitch">
                      {{ isSettingEnabled('useSilentPrint') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                    </label>
                  </div>
                </div>
                <select
                  id="useSilentPrint"
                  name="useSilentPrint"
                  class="form-control"
                  [(ngModel)]="settingsMap['useSilentPrint']"
                  [disabled]="!isSettingEnabled('useSilentPrint')">
                  <option value="true">{{ 'SETTINGS.ENABLED_OPTION' | translate }}</option>
                  <option value="false">{{ 'SETTINGS.DISABLED_OPTION' | translate }}</option>
                </select>
                <small class="form-text text-muted">
                  {{ 'SETTINGS.SILENT_PRINT_HELP' | translate }}
                </small>
              </div>

              <!-- Printer Template -->
              <div class="form-group mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label for="printerTemplate">{{ 'SETTINGS.PRINTER_TEMPLATE' | translate }}</label>
                  <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="printerTemplateSwitch"
                           [checked]="isSettingEnabled('printerTemplate')"
                           (change)="toggleSetting('printerTemplate')">
                    <label class="custom-control-label" for="printerTemplateSwitch">
                      {{ isSettingEnabled('printerTemplate') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                    </label>
                  </div>
                </div>
                <select
                  id="printerTemplate"
                  name="printerTemplate"
                  class="form-control"
                  [(ngModel)]="settingsMap['printerTemplate']"
                  [disabled]="!isSettingEnabled('printerTemplate')">
                  <option *ngFor="let option of printerTemplateOptions" [value]="option.value">{{ option.label }}
                  </option>
                </select>
                <small class="form-text text-muted">
                  {{ 'SETTINGS.PRINTER_TEMPLATE_HELP' | translate }}
                </small>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="settings-section">
              <!-- Custom Invoice Footer -->
              <div class="form-group mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label for="customInvoiceFooter">Custom Invoice Footer</label>
                  <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="customInvoiceFooterSwitch"
                           [checked]="isSettingEnabled('customInvoiceFooter')"
                           (change)="toggleSetting('customInvoiceFooter')">
                    <label class="custom-control-label" for="customInvoiceFooterSwitch">
                      {{ isSettingEnabled('customInvoiceFooter') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                    </label>
                  </div>
                </div>
                <textarea
                  id="customInvoiceFooter"
                  name="customInvoiceFooter"
                  class="form-control"
                  rows="3"
                  [(ngModel)]="settingsMap['customInvoiceFooter']"
                  [disabled]="!isSettingEnabled('customInvoiceFooter')"
                  placeholder="Enter custom text to display at the bottom of invoices (e.g., business hours, contact info, promotional message)">
                </textarea>
                <small class="form-text text-muted">
                  Custom text to display at the bottom of invoices after "Thank you. Come Again". Leave empty to show no
                  custom footer.
                </small>
              </div>

              <!-- Custom Footer Size -->
              <div class="form-group mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label for="customFooterSize">{{ 'SETTINGS.CUSTOM_FOOTER_SIZE' | translate }}</label>
                  <div class="custom-control custom-switch">
                    <input type="checkbox" class="custom-control-input" id="customFooterSizeSwitch"
                           [checked]="isSettingEnabled('customFooterSize')"
                           (change)="toggleSetting('customFooterSize')">
                    <label class="custom-control-label" for="customFooterSizeSwitch">
                      {{ isSettingEnabled('customFooterSize') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                    </label>
                  </div>
                </div>
                <input
                  type="number"
                  id="customFooterSize"
                  name="customFooterSize"
                  class="form-control"
                  [(ngModel)]="settingsMap['customFooterSize']"
                  [disabled]="!isSettingEnabled('customFooterSize')"
                  min="8"
                  max="24"
                  placeholder="12">
                <small class="form-text text-muted">
                  Font size for custom footer text in pixels (8-24px). Default is 12px.
                </small>
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between">
          <button type="button" class="btn btn-secondary" (click)="resetToDefaults()">
            <i class="fas fa-undo"></i> {{ 'SETTINGS.RESET_TO_DEFAULTS' | translate }}
          </button>
          <button type="button" class="btn btn-primary" (click)="saveSettings()">
            <i class="fas fa-save"></i> {{ 'SETTINGS.SAVE_SETTINGS' | translate }}
          </button>
        </div>
      </form>
    </div>

    <!-- Currency Settings Tab -->
    <div class="tab-pane" [class.active]="isTabActive('currency')" *ngIf="isTabActive('currency')">
      <form #currencySettingsForm="ngForm">
        <div class="row mb-4">
          <div class="col-md-8">
            <div class="settings-section">
              <div class="form-group mb-4">
                <label class="mb-3"><strong>Currency Settings</strong></label>

                <!-- Default Currency -->
                <div class="form-group mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <label for="defaultCurrency">Default Currency</label>
                    <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="defaultCurrencySwitch"
                             [checked]="isSettingEnabled('defaultCurrency')"
                             (change)="toggleSetting('defaultCurrency')">
                      <label class="custom-control-label" for="defaultCurrencySwitch">
                        {{ isSettingEnabled('defaultCurrency') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                      </label>
                    </div>
                  </div>
                  <select
                    id="defaultCurrency"
                    name="defaultCurrency"
                    class="form-control"
                    [(ngModel)]="settingsMap['defaultCurrency']"
                    (change)="onCurrencyChange()"
                    [disabled]="!isSettingEnabled('defaultCurrency')">
                    <option *ngFor="let option of currencyOptions" [value]="option.value">
                      {{ option.label }}
                    </option>
                  </select>
                  <small class="form-text text-muted">
                    Select the default currency for the application
                  </small>
                </div>

                <!-- Currency Symbol -->
                <div class="form-group mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <label for="currencySymbol">Currency Symbol</label>
                    <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="currencySymbolSwitch"
                             [checked]="isSettingEnabled('currencySymbol')"
                             (change)="toggleSetting('currencySymbol')">
                      <label class="custom-control-label" for="currencySymbolSwitch">
                        {{ isSettingEnabled('currencySymbol') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                      </label>
                    </div>
                  </div>
                  <input
                    type="text"
                    id="currencySymbol"
                    name="currencySymbol"
                    class="form-control"
                    [(ngModel)]="settingsMap['currencySymbol']"
                    [disabled]="!isSettingEnabled('currencySymbol')"
                    maxlength="5"
                    placeholder="₹">
                  <small class="form-text text-muted">
                    Symbol to display with currency amounts (auto-updated when currency changes)
                  </small>
                </div>

                <!-- Currency Position -->
                <div class="form-group mb-3">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <label for="currencyPosition">Currency Position</label>
                    <div class="custom-control custom-switch">
                      <input type="checkbox" class="custom-control-input" id="currencyPositionSwitch"
                             [checked]="isSettingEnabled('currencyPosition')"
                             (change)="toggleSetting('currencyPosition')">
                      <label class="custom-control-label" for="currencyPositionSwitch">
                        {{ isSettingEnabled('currencyPosition') ? ('COMMON.ENABLED' | translate) : ('COMMON.DISABLED' | translate) }}
                      </label>
                    </div>
                  </div>
                  <select
                    id="currencyPosition"
                    name="currencyPosition"
                    class="form-control"
                    [(ngModel)]="settingsMap['currencyPosition']"
                    [disabled]="!isSettingEnabled('currencyPosition')">
                    <option *ngFor="let option of currencyPositionOptions" [value]="option.value">
                      {{ option.label }}
                    </option>
                  </select>
                  <small class="form-text text-muted">
                    Position of currency symbol relative to the amount
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between">
          <button type="button" class="btn btn-secondary" (click)="resetToDefaults()">
            <i class="fas fa-undo"></i> {{ 'SETTINGS.RESET_TO_DEFAULTS' | translate }}
          </button>
          <button type="button" class="btn btn-primary" (click)="saveSettings()">
            <i class="fas fa-save"></i> {{ 'SETTINGS.SAVE_SETTINGS' | translate }}
          </button>
        </div>
      </form>
    </div>

    <!-- Barcode Settings Tab -->
    <div class="tab-pane" [class.active]="isTabActive('barcode')" *ngIf="isTabActive('barcode')">
      <div class="barcode-settings-container">
        <div class="row">
          <!-- Left Column: Settings -->
          <div class="col-md-8">
            <form #barcodeForm="ngForm">

              <!-- Basic Settings Section -->
              <div class="settings-section mb-4">
                <h5 class="section-title">
                  <i class="fas fa-cog"></i> Basic Settings
                </h5>

                <div class="row">
                  <div class="col-md-6">
                    <!-- Paper Size -->
                    <div class="form-group mb-3">
                      <label for="paperSize">Paper Size</label>
                      <select
                        id="paperSize"
                        name="paperSize"
                        class="form-control"
                        [(ngModel)]="barcodeSettings.paperSize">
                        <option *ngFor="let option of paperSizeOptions" [value]="option.value">
                          {{option.label}}
                        </option>
                      </select>
                      <small class="form-text text-muted">Select the paper size for barcode stickers</small>
                    </div>

                    <!-- Columns -->
                    <div class="form-group mb-3">
                      <label for="columns">Columns</label>
                      <select
                        id="columns"
                        name="columns"
                        class="form-control"
                        [(ngModel)]="barcodeSettings.columns">
                        <option *ngFor="let option of columnOptions" [value]="option.value">
                          {{option.label}}
                        </option>
                      </select>
                      <small class="form-text text-muted">Number of columns for barcode layout</small>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <!-- Show Barcode -->
                    <div class="form-group mb-3">
                      <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="showBarcode"
                          [(ngModel)]="barcodeSettings.showBarcode" name="showBarcode">
                        <label class="custom-control-label" for="showBarcode">
                          Show Barcode Element
                        </label>
                      </div>
                      <small class="form-text text-muted">Display the actual barcode image</small>
                    </div>

                    <!-- Use Cost Codes -->
                    <div class="form-group mb-3">
                      <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="useCostCodes"
                          [(ngModel)]="barcodeSettings.useCostCodes" name="useCostCodes">
                        <label class="custom-control-label" for="useCostCodes">
                          Use Cost Codes
                        </label>
                      </div>
                      <small class="form-text text-muted">Replace numeric codes with letters</small>

                      <!-- Cost Code Settings Button -->
                      <button type="button"
                              class="btn btn-outline-info btn-sm mt-2"
                              (click)="openCostCodeSettings()"
                              [disabled]="isSaving">
                        <i class="fas fa-cog"></i> Cost Code Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Font Settings Section -->
              <div class="settings-section mb-4">
                <h5 class="section-title">
                  <i class="fas fa-font"></i> Font Settings
                </h5>

                <!-- Font Family -->
                <div class="form-group mb-3">
                  <label for="fontFamily">Font Family</label>
                  <select
                    id="fontFamily"
                    name="fontFamily"
                    class="form-control"
                    [(ngModel)]="barcodeSettings.fontFamily">
                    <option *ngFor="let option of fontFamilyOptions" [value]="option.value">
                      {{option.label}}
                    </option>
                  </select>
                  <small class="form-text text-muted">Choose a font family that's available on all computers</small>
                </div>

                <!-- Font Sizes -->
                <div class="row">
                  <div class="col-md-6">
                    <!-- Item Name Font Size -->
                    <div class="form-group mb-3">
                      <label for="itemNameFontSize">Item Name Font Size (px)</label>
                      <div class="input-group">
                        <input
                          type="number"
                          id="itemNameFontSize"
                          name="itemNameFontSize"
                          class="form-control"
                          [(ngModel)]="barcodeSettings.itemNameFontSize"
                          [min]="getFontSizeRange().min"
                          [max]="getFontSizeRange().max"
                          (blur)="validateFontSize(barcodeSettings.itemNameFontSize, 'Item Name')">
                        <div class="input-group-append">
                          <span class="input-group-text">px</span>
                        </div>
                      </div>
                      <small class="form-text text-muted">Font size for product names ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
                    </div>

                    <!-- Price Font Size -->
                    <div class="form-group mb-3">
                      <label for="priceFontSize">Price Font Size (px)</label>
                      <div class="input-group">
                        <input
                          type="number"
                          id="priceFontSize"
                          name="priceFontSize"
                          class="form-control"
                          [(ngModel)]="barcodeSettings.priceFontSize"
                          [min]="getFontSizeRange().min"
                          [max]="getFontSizeRange().max"
                          (blur)="validateFontSize(barcodeSettings.priceFontSize, 'Price')">
                        <div class="input-group-append">
                          <span class="input-group-text">px</span>
                        </div>
                      </div>
                      <small class="form-text text-muted">Font size for prices ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <!-- Cost Code Font Size -->
                    <div class="form-group mb-3">
                      <label for="costCodeFontSize">Cost Code Font Size (px)</label>
                      <div class="input-group">
                        <input
                          type="number"
                          id="costCodeFontSize"
                          name="costCodeFontSize"
                          class="form-control"
                          [(ngModel)]="barcodeSettings.costCodeFontSize"
                          [min]="getFontSizeRange().min"
                          [max]="getFontSizeRange().max"
                          (blur)="validateFontSize(barcodeSettings.costCodeFontSize, 'Cost Code')">
                        <div class="input-group-append">
                          <span class="input-group-text">px</span>
                        </div>
                      </div>
                      <small class="form-text text-muted">Font size for cost codes ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
                    </div>

                    <!-- Barcode Text Font Size -->
                    <div class="form-group mb-3">
                      <label for="barcodeTextFontSize">Barcode Text Font Size (px)</label>
                      <div class="input-group">
                        <input
                          type="number"
                          id="barcodeTextFontSize"
                          name="barcodeTextFontSize"
                          class="form-control"
                          [(ngModel)]="barcodeSettings.barcodeTextFontSize"
                          [min]="getFontSizeRange().min"
                          [max]="getFontSizeRange().max"
                          (blur)="validateFontSize(barcodeSettings.barcodeTextFontSize, 'Barcode Text')">
                        <div class="input-group-append">
                          <span class="input-group-text">px</span>
                        </div>
                      </div>
                      <small class="form-text text-muted">Font size for barcode text below barcode ({{getFontSizeRange().min}}-{{getFontSizeRange().max}}px)</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="d-flex justify-content-between">
                <div>
                  <button type="button" class="btn btn-secondary mr-2" (click)="resetBarcodeToDefaults()" [disabled]="isSaving">
                    <i class="fas fa-undo"></i> Reset to Defaults
                  </button>
                  <button type="button" class="btn btn-warning mr-2" (click)="clearBarcodeLocalStorage()" [disabled]="isSaving">
                    <i class="fas fa-trash"></i> Clear Browser Memory
                  </button>

                </div>
                <button type="button" class="btn btn-primary" (click)="saveBarcodeSettings()" [disabled]="isSaving">
                  <i class="fas fa-save"></i>
                  <span *ngIf="!isSaving">Save Settings</span>
                  <span *ngIf="isSaving">Saving...</span>
                </button>
              </div>
            </form>
          </div>

          <!-- Right Column: Preview -->
          <div class="col-md-4">
            <div class="preview-section">
              <h5 class="section-title">
                <i class="fas fa-eye"></i> Font Preview
              </h5>

              <div class="preview-container">
                <div class="preview-item mb-3">
                  <label>Item Name:</label>
                  <div class="preview-text" [ngStyle]="getPreviewStyle('itemName')">
                    Sample Product Name
                  </div>
                </div>

                <div class="preview-item mb-3">
                  <label>Price:</label>
                  <div class="preview-text" [ngStyle]="getPreviewStyle('price')">
                    25.50
                  </div>
                </div>

                <div class="preview-item mb-3" *ngIf="barcodeSettings.useCostCodes">
                  <label>Cost Code:</label>
                  <div class="preview-text" [ngStyle]="getPreviewStyle('costCode')">
                    ABC
                  </div>
                </div>

                <div class="preview-item mb-3" *ngIf="barcodeSettings.showBarcode">
                  <label>Barcode Text:</label>
                  <div class="preview-text" [ngStyle]="getPreviewStyle('barcodeText')">
                    1234567890
                  </div>
                </div>
              </div>

              <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle"></i>
                <strong>Note:</strong> These font sizes will be applied to all barcode components.
                Test on the target computer to ensure proper visibility.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


