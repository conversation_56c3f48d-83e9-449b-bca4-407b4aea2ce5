import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { UserSettingsService } from '../../service/user-settings.service';
import { UserSettings } from '../../model/user-settings';
import { SettingsAccessService } from '../../service/settings-access.service';
import { GeneralSettingsService } from '../../service/general-settings.service';
import { BarcodeSettingsService } from '../../../core/service/barcode-settings.service';
import { Settings } from '../../../core/model/settings';
import { BarcodeSettings, BarcodeSettingsModel } from '../../../core/model/barcode-settings';
import { TranslateService } from '../../../../translate.service';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { CostCodeSetupComponent } from '../cost-code-setup/cost-code-setup.component';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-general-settings',
  templateUrl: './general-settings.component.html',
  styleUrls: ['./general-settings.component.css']
})
export class GeneralSettingsComponent implements OnInit {
  // Constants for localStorage keys
  private readonly SETTINGS_STORAGE_KEY = 'app_settings';
  private readonly SETTINGS_ENABLED_KEY = 'app_settings_enabled';
  private readonly SETTINGS_TIMESTAMP_KEY = 'app_settings_timestamp';

  // Settings data
  settingsMap: { [key: string]: string } = {};
  enabledMap: { [key: string]: boolean } = {};
  currentLang: string;
  isLoading = true;

  // Tab management
  activeTab: string = 'general';

  // Options for dropdown menus - will be loaded from the database
  discountModeOptions: { value: string, label: string }[] = [];

  // Printer template options will be loaded from the database
  printerTemplateOptions: { value: string, label: string }[] = [];

  // Currency options - will be loaded from the database
  currencyOptions: { value: string, label: string, symbol: string }[] = [];

  // Currency position options - will be dynamically generated
  currencyPositionOptions: { value: string, label: string }[] = [];



  // List of general settings keys
  generalSettingKeys = [
    'defaultDiscountMode',
    'printerTemplate',
    'useSilentPrint',
    'minimumMarkupPercentage',
    'allowSellingUnderCost',
    'defaultCurrency',
    'currencySymbol',
    'currencyPosition',
    'customInvoiceFooter',
    'customFooterSize',
    'useCostCodes',
    'showBarcode',
    'barcodePrintRows',
    'barcodePrintColumns',
    'barcodePaperSize'
  ];

  constructor(
    private toastr: ToastrService,
    private generalSettingsService: GeneralSettingsService,
    private barcodeSettingsService: BarcodeSettingsService,
    private translateService: TranslateService,
    private modalService: BsModalService
  ) {
    // Initialize with the current language from localStorage
    this.currentLang = localStorage.getItem('lang') || 'en';
    this.barcodeSettings = BarcodeSettingsModel.getDefaultSettings();
  }

  ngOnInit(): void {
    this.loadSettings();
    this.loadPrinterTemplates();
    this.loadDiscountModeOptions();
    this.loadCurrencyOptions();
    this.loadCurrencyPositionOptions();
    this.loadBarcodeSettings();
  }

  /**
   * Load all settings from database and apply user preferences from localStorage
   */
  loadSettings(): void {
    // First try to load from localStorage for immediate display
    this.loadFromLocalStorage();

    // Then load from server to get the latest database values
    this.generalSettingsService.getAllSettings().subscribe(
      (settings) => {
        console.log('Loaded settings from database:', settings);

        // Create a map of database settings
        const dbSettings = {};
        if (Array.isArray(settings)) {
          settings.forEach(setting => {
            if (this.generalSettingKeys.includes(setting.key)) {
              dbSettings[setting.key] = setting.value;
            }
          });
        }

        // For each setting key, use the localStorage value if available, otherwise use the database value
        this.generalSettingKeys.forEach(key => {
          // If we don't have this setting in localStorage (user preference), use the database value
          if (this.settingsMap[key] === this.getDefaultValue(key) && dbSettings[key]) {
            this.settingsMap[key] = dbSettings[key];
          }
          // Otherwise keep the localStorage value (user preference)
        });

        console.log('Final settings after merging with database:', this.settingsMap);
        this.isLoading = false;
      },
      (error) => {
        console.error('Error loading settings from database:', error);
        this.toastr.error('Failed to load settings from server', 'Error');
        this.isLoading = false;
      }
    );
  }

  /**
   * Get default value for a setting
   */
  getDefaultValue(key: string): string {
    const defaults = {
      'defaultDiscountMode': 'percentage',
      'printerTemplate': '',
      'useSilentPrint': 'false',
      'minimumMarkupPercentage': '4',
      'allowSellingUnderCost': 'false',
      'defaultCurrency': 'LKR',
      'currencySymbol': 'රු',
      'currencyPosition': 'before',
      'customInvoiceFooter': '',
      'customFooterSize': '12',
      'useCostCodes': 'false',
      'showBarcode': 'true',
      'barcodePrintRows': '3',
      'barcodePrintColumns': '2',
      'barcodePaperSize': '30x20'
    };
    return defaults[key] || '';
  }

  /**
   * Load settings from localStorage - simplified version that only loads key-value pairs
   */
  loadFromLocalStorage(): void {
    try {
      // Initialize with default values first
      this.generalSettingKeys.forEach(key => {
        this.settingsMap[key] = this.getDefaultValue(key);
      });

      // Load settings from localStorage if available
      const storedSettings = localStorage.getItem(this.SETTINGS_STORAGE_KEY);
      if (storedSettings) {
        try {
          const parsedSettings = JSON.parse(storedSettings);
          console.log('Loaded settings from localStorage:', parsedSettings);

          // Only update the keys we care about
          this.generalSettingKeys.forEach(key => {
            if (parsedSettings[key] !== undefined) {
              this.settingsMap[key] = parsedSettings[key];
            }
          });
        } catch (parseError) {
          console.error('Error parsing settings from localStorage:', parseError);
        }
      } else {
        console.log('No settings found in localStorage, using defaults');
      }

      // Load enabled settings
      const storedEnabled = localStorage.getItem(this.SETTINGS_ENABLED_KEY);
      if (storedEnabled) {
        try {
          this.enabledMap = JSON.parse(storedEnabled);
        } catch (parseError) {
          console.error('Error parsing enabled settings from localStorage:', parseError);
        }
      }

      // Enable all settings by default
      this.generalSettingKeys.forEach(key => {
        if (this.enabledMap[key] === undefined) {
          this.enabledMap[key] = true;
        }
      });

      // Always enable the allowSellingUnderCost setting since it doesn't have a toggle
      this.enabledMap['allowSellingUnderCost'] = true;

      console.log('Final settings after loading from localStorage:', this.settingsMap);
    } catch (error) {
      console.error('Error loading settings from localStorage:', error);
    }
  }

  /**
   * Save settings to localStorage - simplified version that only stores key-value pairs
   */
  saveToLocalStorage(): void {
    try {
      // Create a simple object with just the key-value pairs
      const simpleSettings = {};

      // Add only the settings we care about
      for (const key of this.generalSettingKeys) {
        if (this.settingsMap[key] !== undefined) {
          simpleSettings[key] = this.settingsMap[key];
        }
      }

      // Save simple settings to localStorage
      localStorage.setItem(this.SETTINGS_STORAGE_KEY, JSON.stringify(simpleSettings));
      localStorage.setItem(this.SETTINGS_ENABLED_KEY, JSON.stringify(this.enabledMap));
      localStorage.setItem(this.SETTINGS_TIMESTAMP_KEY, Date.now().toString());

      console.log('Simple settings saved to localStorage:', simpleSettings);
    } catch (error) {
      console.error('Error saving settings to localStorage:', error);
    }
  }

  /**
   * Get the appropriate category for a setting key
   */
  getCategoryForKey(key: string): string {
    const categories = {
      'printerTemplate': 'Printing',
      'useSilentPrint': 'Printing',
      'customInvoiceFooter': 'Printing',
      'defaultLanguage': 'System',
      'defaultDiscountMode': 'Invoice',
      'minimumMarkupPercentage': 'Invoice',
      'allowSellingUnderCost': 'Invoice',
      'defaultCurrency': 'System',
      'currencySymbol': 'System',
      'currencyPosition': 'System',
      'useCostCodes': 'Barcode',
      'showBarcode': 'Barcode',
      'barcodeFontSize': 'Barcode',
      'barcodePrintRows': 'Barcode',
      'barcodePrintColumns': 'Barcode',
      'barcodePaperSize': 'Barcode'
    };

    return categories[key] || 'Invoice';
  }

  /**
   * Get a description for a setting key
   */
  getDescriptionForKey(key: string): string {
    const descriptions = {
      'printerTemplate': 'Default printer template',
      'useSilentPrint': 'Whether to use silent printing (no print dialog)',
      'customInvoiceFooter': 'Custom text to display at the bottom of invoices after "Thank you. Come Again"',
      'customFooterSize': 'Font size for custom footer text (in pixels)',
      'defaultLanguage': 'Default system language (en or sn)',
      'defaultDiscountMode': 'Default discount mode (percentage or flat)',
      'minimumMarkupPercentage': 'Minimum markup percentage above cost for CASHIER role',
      'allowSellingUnderCost': 'Whether to allow selling items below cost',
      'defaultCurrency': 'Default currency code (LKR, USD, EUR, etc.)',
      'currencySymbol': 'Currency symbol to display in the application',
      'currencyPosition': 'Position of currency symbol relative to amount',
      'useCostCodes': 'Use custom cost codes instead of traditional barcodes',
      'showBarcode': 'Whether to display barcode images on barcode stickers',
      'barcodeFontSize': 'Font size for barcode text and labels',
      'barcodePrintRows': 'Number of barcode rows per page when printing',
      'barcodePrintColumns': 'Number of barcode columns per row when printing',
      'barcodePaperSize': 'Paper size for barcode printing'
    };

    return descriptions[key] || '';
  }

  /**
   * Handle currency change and auto-update symbol
   */
  onCurrencyChange(): void {
    const selectedCurrency = this.settingsMap['defaultCurrency'];
    const currencyOption = this.currencyOptions.find(option => option.value === selectedCurrency);

    if (currencyOption) {
      this.settingsMap['currencySymbol'] = currencyOption.symbol;
      console.log('Auto-updated currency symbol to:', currencyOption.symbol);

      // Update position options to show the new symbol
      this.updateCurrencyPositionOptions();
    }
  }

  /**
   * Save all settings
   */
  saveSettings(): void {
    // Create an array of settings to save
    const settingsToSave = this.generalSettingKeys.map(key => {
      const setting = new Settings();
      setting.key = key;
      setting.value = this.settingsMap[key] || this.getDefaultValue(key);
      setting.category = this.getCategoryForKey(key);
      setting.description = this.getDescriptionForKey(key);
      return setting;
    });

    // Save each setting
    let savedCount = 0;
    let errorCount = 0;

    // First, save the user preferences to localStorage immediately
    // This ensures the user's choices are remembered even if the server save fails
    this.saveToLocalStorage();

    // Then save to the database
    settingsToSave.forEach(setting => {
      this.generalSettingsService.saveSetting(setting).subscribe(
        (response) => {
          // The controller now returns the saved setting directly
          if (response && response.key) {
            // Direct Settings object
            const savedSetting = response;
            console.log(`Setting saved to database: ${savedSetting.key} = ${savedSetting.value}`);
          } else {
            console.warn('Unexpected response format:', response);
          }

          savedCount++;
          if (savedCount + errorCount === settingsToSave.length) {
            this.toastr.success('Settings saved successfully', 'Success');
          }
        },
        (error) => {
          console.error(`Error saving setting ${setting.key}:`, error);
          errorCount++;
          if (savedCount + errorCount === settingsToSave.length) {
            if (errorCount > 0) {
              this.toastr.warning(`${errorCount} settings failed to save to database`, 'Warning');
            } else {
              this.toastr.success('Settings saved successfully', 'Success');
            }
          }
        }
      );
    });
  }

  /**
   * Toggle a setting on/off
   */
  toggleSetting(key: string): void {
    const currentEnabled = this.enabledMap[key] || false;
    this.enabledMap[key] = !currentEnabled;

    // Save to localStorage immediately
    this.saveToLocalStorage();

    this.toastr.success(`Setting ${!currentEnabled ? 'enabled' : 'disabled'}`, 'Success');
  }

  /**
   * Reset settings to defaults
   */
  resetToDefaults(): void {
    // Clear localStorage first
    localStorage.removeItem(this.SETTINGS_STORAGE_KEY);
    localStorage.removeItem(this.SETTINGS_ENABLED_KEY);
    localStorage.removeItem(this.SETTINGS_TIMESTAMP_KEY);

    // Reset to default values
    this.generalSettingKeys.forEach(key => {
      this.settingsMap[key] = this.getDefaultValue(key);
    });

    // Enable all settings by default
    this.generalSettingKeys.forEach(key => {
      this.enabledMap[key] = true;
    });

    // Always enable the allowSellingUnderCost setting since it doesn't have a toggle
    this.enabledMap['allowSellingUnderCost'] = true;

    // Reload settings from the database
    this.generalSettingsService.getAllSettings().subscribe(
      (settings) => {
        if (Array.isArray(settings)) {
          settings.forEach(setting => {
            if (this.generalSettingKeys.includes(setting.key)) {
              this.settingsMap[setting.key] = setting.value;
            }
          });
        }
        this.toastr.info('Settings reset to database defaults', 'Reset');
      },
      (error) => {
        console.error('Error loading settings from database:', error);
        this.toastr.info('Settings reset to system defaults', 'Reset');
      }
    );
  }

  /**
   * Check if a setting is enabled
   */
  isSettingEnabled(key: string): boolean {
    return this.enabledMap[key] || false;
  }

  /**
   * Switch to a specific tab
   */
  switchTab(tabName: string): void {
    this.activeTab = tabName;
  }

  /**
   * Check if a tab is active
   */
  isTabActive(tabName: string): boolean {
    return this.activeTab === tabName;
  }

  /**
   * Load printer templates from the database
   */
  loadPrinterTemplates(): void {
    // First try to load from localStorage for immediate display
    const cachedTemplates = localStorage.getItem('app_printer_templates');
    if (cachedTemplates) {
      try {
        this.printerTemplateOptions = JSON.parse(cachedTemplates);
        return;
      } catch (error) {
        console.error('Error parsing printer templates from localStorage:', error);
      }
    }

    // If not in localStorage, load from the database
    this.generalSettingsService.getSettingsByCategory('Printing').subscribe(
      (printingSettings) => {
        // Look for the printerTemplates setting
        const templatesSettings = printingSettings.find(s => s.key === 'printerTemplates');
        if (templatesSettings && templatesSettings.value) {
          const templateKeys = templatesSettings.value.split(',');

          if (templateKeys.length > 0) {
            this.printerTemplateOptions = [];

            templateKeys.forEach(key => {
              // Look for the template_X setting
              const templateSetting = printingSettings.find(s => s.key === 'template_' + key);
              if (templateSetting && templateSetting.value) {
                const template = { value: key, label: templateSetting.value };
                this.printerTemplateOptions.push(template);
              } else {
                // If no template_X setting, use the key as both value and label
                const template = { value: key, label: key };
                this.printerTemplateOptions.push(template);
              }
            });

            // Cache in localStorage for faster access next time
            localStorage.setItem('app_printer_templates', JSON.stringify(this.printerTemplateOptions));
            return;
          }
        }

        // If we couldn't load templates, use default values
        this.setDefaultPrinterTemplates();
      },
      (error) => {
        console.error('Error loading printer templates from API:', error);
        this.setDefaultPrinterTemplates();
      }
    );
  }

  /**
   * Set default printer templates when they can't be loaded from the database
   */
  setDefaultPrinterTemplates(): void {
    this.printerTemplateOptions = [
      { value: '58mm_English', label: '58mm English' },
      { value: '76mm_English', label: '76mm English' },
      { value: '80mm_English', label: '80mm English' },
      { value: '80mm_Sinhala', label: '80mm Sinhala' },
      { value: 'Legal_English', label: 'Legal English' },
      { value: 'Legal_Customised_English', label: 'Legal Customised English' }
    ];

    // Cache in localStorage for faster access next time
    localStorage.setItem('app_printer_templates', JSON.stringify(this.printerTemplateOptions));
  }

  /**
   * Load discount mode options from the database
   */
  loadDiscountModeOptions(): void {
    // First try to load from localStorage for immediate display
    const cachedOptions = localStorage.getItem('app_discount_mode_options');
    if (cachedOptions) {
      try {
        this.discountModeOptions = JSON.parse(cachedOptions);
        return;
      } catch (error) {
        console.error('Error parsing discount mode options from localStorage:', error);
      }
    }

    // If not in localStorage, load from the database
    this.generalSettingsService.getSettingsByCategory('Invoice').subscribe(
      (invoiceSettings) => {
        // Look for the discountModeOptions setting
        const optionsSettings = invoiceSettings.find(s => s.key === 'discountModeOptions');
        if (optionsSettings && optionsSettings.value) {
          try {
            // The value should be a JSON string containing the options
            const options = JSON.parse(optionsSettings.value);
            if (Array.isArray(options) && options.length > 0) {
              this.discountModeOptions = options;

              // Cache in localStorage for faster access next time
              localStorage.setItem('app_discount_mode_options', JSON.stringify(this.discountModeOptions));
              return;
            }
          } catch (error) {
            console.error('Error parsing discount mode options from database:', error);
          }
        }

        // If we couldn't load options, use default values
        this.setDefaultDiscountModeOptions();
      },
      (error) => {
        console.error('Error loading discount mode options from API:', error);
        this.setDefaultDiscountModeOptions();
      }
    );
  }

  /**
   * Set default discount mode options when they can't be loaded from the database
   */
  setDefaultDiscountModeOptions(): void {
    this.discountModeOptions = [
      { value: 'percentage', label: 'Percentage' },
      { value: 'flat', label: 'Flat' }
    ];

    // Cache in localStorage for faster access next time
    localStorage.setItem('app_discount_mode_options', JSON.stringify(this.discountModeOptions));
  }

  /**
   * Load currency options from the database
   */
  loadCurrencyOptions(): void {
    // First try to load from localStorage for immediate display
    const cachedOptions = localStorage.getItem('app_currency_options');
    if (cachedOptions) {
      try {
        this.currencyOptions = JSON.parse(cachedOptions);
        return;
      } catch (error) {
        console.error('Error parsing currency options from localStorage:', error);
      }
    }

    // If not in localStorage, load from the database
    this.generalSettingsService.getSettingsByCategory('System').subscribe(
      (systemSettings) => {
        // Look for the currencyOptions setting
        const optionsSettings = systemSettings.find(s => s.key === 'currencyOptions');
        if (optionsSettings && optionsSettings.value) {
          try {
            // The value should be a JSON string containing the options
            const options = JSON.parse(optionsSettings.value);
            if (Array.isArray(options) && options.length > 0) {
              this.currencyOptions = options;

              // Cache in localStorage for faster access next time
              localStorage.setItem('app_currency_options', JSON.stringify(this.currencyOptions));
              return;
            }
          } catch (error) {
            console.error('Error parsing currency options from database:', error);
          }
        }

        // If we couldn't load options, use default values
        this.setDefaultCurrencyOptions();
      },
      (error) => {
        console.error('Error loading currency options from API:', error);
        this.setDefaultCurrencyOptions();
      }
    );
  }

  /**
   * Set default currency options when they can't be loaded from the database
   */
  setDefaultCurrencyOptions(): void {
    this.currencyOptions = [
      { value: 'LKR', label: 'Sri Lankan Rupee (LKR)', symbol: 'රු' },
      { value: 'USD', label: 'US Dollar (USD)', symbol: '$' },
      { value: 'EUR', label: 'Euro (EUR)', symbol: '€' },
      { value: 'GBP', label: 'British Pound (GBP)', symbol: '£' },
      { value: 'INR', label: 'Indian Rupee (INR)', symbol: '₹' },
      { value: 'JPY', label: 'Japanese Yen (JPY)', symbol: '¥' },
      { value: 'AUD', label: 'Australian Dollar (AUD)', symbol: 'A$' },
      { value: 'CAD', label: 'Canadian Dollar (CAD)', symbol: 'C$' }
    ];

    // Cache in localStorage for faster access next time
    localStorage.setItem('app_currency_options', JSON.stringify(this.currencyOptions));
  }

  /**
   * Load currency position options (dynamically generated based on current currency)
   */
  loadCurrencyPositionOptions(): void {
    this.updateCurrencyPositionOptions();
  }

  /**
   * Update currency position options based on current currency symbol
   */
  updateCurrencyPositionOptions(): void {
    const currentSymbol = this.settingsMap['currencySymbol'] || 'රු';

    this.currencyPositionOptions = [
      { value: 'before', label: `Before Amount (${currentSymbol}1,000)` },
      { value: 'after', label: `After Amount (1,000${currentSymbol})` }
    ];
  }

  /**
   * Switch between languages
   * @param lang Language code ('en' or 'sn')
   */
  switchLanguage(lang: string) {
    // Update the current language in the component
    this.currentLang = lang;

    // Show a message to the user
    this.toastr.info(`Changing language to ${lang === 'en' ? 'English' : 'Sinhala'}`, 'Language');

    // Use the translation service to load the new language
    this.translateService.use(lang).then(() => {
      // Force a hard refresh to ensure all components are updated
      window.location.href = window.location.href.split('#')[0]; // Remove any hash to ensure a full reload
    }).catch(error => {
      console.error('Error switching language:', error);
      this.toastr.error(`Error switching language to ${lang}. Please try again.`, 'Error');
    });
  }

  /**
   * Clear settings from localStorage
   */
  clearLocalStorage(): void {
    if (confirm('Are you sure you want to clear settings from browser memory? This will reset to default settings.')) {
      this.generalSettingsService.clearAllSettingsFromLocalStorage();
      this.loadSettings();
      this.toastr.info('Browser memory cleared. Settings reset to defaults.', 'Cleared');
    }
  }

  // ===== BARCODE SETTINGS METHODS =====

  /**
   * Load barcode settings
   */
  loadBarcodeSettings(): void {
    // First load from localStorage for immediate display
    this.loadBarcodeFromLocalStorage();

    // Then load from database to get the latest values
    this.barcodeSettingsService.loadBarcodeSettingsFromDatabase().subscribe(
      (settings) => {
        // Merge database settings with localStorage settings
        // Keep localStorage values as user preferences, but update with database values if localStorage doesn't have them
        if (!localStorage.getItem('app_settings')) {
          this.barcodeSettings = settings;
        }
        console.log('Barcode settings loaded and merged:', this.barcodeSettings);
      },
      (error) => {
        console.error('Error loading barcode settings from database:', error);
        // Keep localStorage settings if database fails
        this.toastr.warning('Using cached barcode settings. Database connection failed.', 'Warning');
      }
    );
  }

  /**
   * Save barcode settings
   */
  saveBarcodeSettings(): void {
    this.isSaving = true;

    // Save to database (this will also save to localStorage automatically)
    this.barcodeSettingsService.saveBarcodeSettingsToDatabase(this.barcodeSettings).subscribe(
      (success) => {
        this.isSaving = false;
        if (success) {
          this.toastr.success('Barcode settings saved successfully', 'Success');
        } else {
          this.toastr.warning('Settings saved locally but some database operations failed', 'Warning');
        }
        console.log('Barcode settings saved to both localStorage and database');
      },
      (error) => {
        this.isSaving = false;
        console.error('Error saving barcode settings to database:', error);
        // Fallback: save to localStorage only if database fails
        this.barcodeSettingsService.saveBarcodeSettings(this.barcodeSettings);
        this.toastr.warning('Settings saved locally but database save failed', 'Warning');
      }
    );
  }

  /**
   * Reset barcode settings to default
   */
  resetBarcodeToDefaults(): void {
    if (confirm('Are you sure you want to reset all barcode settings to defaults? This action cannot be undone.')) {
      this.barcodeSettings = BarcodeSettingsModel.getDefaultSettings();
      this.saveBarcodeSettings();
      this.toastr.info('Barcode settings reset to defaults', 'Reset');
    }
  }

  /**
   * Load barcode settings from localStorage for immediate display
   */
  loadBarcodeFromLocalStorage(): void {
    this.barcodeSettings = this.barcodeSettingsService.getBarcodeSettingsFromGeneralSettings();
    console.log('Barcode settings loaded from localStorage:', this.barcodeSettings);
  }

  /**
   * Clear barcode settings from localStorage
   */
  clearBarcodeLocalStorage(): void {
    if (confirm('Are you sure you want to clear barcode settings from browser memory? This will reset to default settings.')) {
      this.barcodeSettingsService.clearBarcodeSettingsFromLocalStorage();
      this.loadBarcodeSettings();
      this.toastr.info('Browser memory cleared. Barcode settings reset to defaults.', 'Cleared');
    }
  }

  /**
   * Preview the current font settings
   */
  getPreviewStyle(element: string): any {
    const baseStyle = {
      fontFamily: this.barcodeSettings.fontFamily,
      fontWeight: 'bold',
      color: '#333'
    };

    switch (element) {
      case 'itemName':
        return { ...baseStyle, fontSize: this.barcodeSettings.itemNameFontSize + 'px' };
      case 'price':
        return { ...baseStyle, fontSize: this.barcodeSettings.priceFontSize + 'px' };
      case 'costCode':
        return { ...baseStyle, fontSize: this.barcodeSettings.costCodeFontSize + 'px' };
      case 'barcodeText':
        return { ...baseStyle, fontSize: this.barcodeSettings.barcodeTextFontSize + 'px' };
      default:
        return baseStyle;
    }
  }

  /**
   * Get font size range validation
   */
  getFontSizeRange(): { min: number, max: number } {
    return { min: 6, max: 24 };
  }

  /**
   * Validate font size
   */
  validateFontSize(value: number, element: string): void {
    const range = this.getFontSizeRange();
    if (value < range.min || value > range.max) {
      this.toastr.warning(`Font size for ${element} should be between ${range.min} and ${range.max} pixels`, 'Validation');
    }
  }

  /**
   * Update font size with validation
   */
  updateFontSize(element: string, value: number): void {
    const range = this.getFontSizeRange();
    const validValue = Math.max(range.min, Math.min(range.max, value));

    switch (element) {
      case 'itemName':
        this.barcodeSettings.itemNameFontSize = validValue;
        break;
      case 'price':
        this.barcodeSettings.priceFontSize = validValue;
        break;
      case 'costCode':
        this.barcodeSettings.costCodeFontSize = validValue;
        break;
      case 'barcodeText':
        this.barcodeSettings.barcodeTextFontSize = validValue;
        break;
    }
  }

  /**
   * Open cost code settings modal
   */
  openCostCodeSettings(): void {
    const modalRef: BsModalRef = this.modalService.show(CostCodeSetupComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: {
        isModal: true
      }
    });

    // Subscribe to modal hidden event to reload settings if needed
    modalRef.onHidden.subscribe(() => {
      // Reload settings to get any updated cost code settings
      this.loadBarcodeSettings();
    });
  }

}
